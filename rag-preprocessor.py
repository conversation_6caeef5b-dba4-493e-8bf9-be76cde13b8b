import pandas as pd
import sys

# --- Configuration: Please update these values ---

# The name of your input CSV file.
INPUT_CSV_PATH = 'rag_small.csv' 

# The name of the file you want to create for RAG ingestion.
OUTPUT_TXT_PATH = 'processed_tickets.txt'

# IMPORTANT: Map the template fields to the ACTUAL column names in your CSV.
# The keys on the left ('id', 'subject', etc.) are used in the template.
# The values on the right ('confidential id', 'subject', etc.) MUST MATCH your CSV headers.
COLUMN_MAPPING = {
    'id': 'confidential id',
    'subject': 'subject',
    'staff': 'staff.id staff.surname staff.firstname',
    'medium': 'medium',
    'body': 'body'
    # Add other columns you want to include, for example:
    # 'date': 'ticket_creation_date' 
}
# --- End of Configuration ---


def preprocess_tickets():
    """
    Reads ticket data from a CSV, formats it into a clean text format,
    and saves it to a .txt file ready for RAG ingestion.
    """
    try:
        # Load the CSV file into a pandas DataFrame.
        # .fillna('') replaces any empty cells with an empty string to avoid errors.
        df = pd.read_csv(INPUT_CSV_PATH).fillna('')
        print(f"✅ Successfully loaded '{INPUT_CSV_PATH}' with {len(df)} records.")
    except FileNotFoundError:
        print(f"❌ Error: The file '{INPUT_CSV_PATH}' was not found.")
        print("Please make sure the CSV file is in the same directory as the script.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ An unexpected error occurred while reading the CSV: {e}")
        sys.exit(1)

    formatted_tickets = []

    # Iterate over each row in the DataFrame.
    for index, row in df.iterrows():
        try:
            # Use an f-string to build the formatted text for each ticket.
            # This format makes it easy for the RAG model to understand the context of each piece of data.
            ticket_text = (
                f"Ticket ID: {row.get(COLUMN_MAPPING['id'], 'N/A')}\n"
                f"Subject: {row.get(COLUMN_MAPPING['subject'], 'N/A')}\n"
                f"Staff: {row.get(COLUMN_MAPPING['staff'], 'N/A')}\n"
                f"Medium: {row.get(COLUMN_MAPPING['medium'], 'N/A')}\n"
                f"Body:\n{row.get(COLUMN_MAPPING['body'], 'N/A')}"
            )
            formatted_tickets.append(ticket_text)
        except KeyError as e:
            print(f"❌ Error: Column not found in CSV -> {e}.")
            print("Please check your COLUMN_MAPPING in the script to ensure it matches your CSV headers.")
            sys.exit(1)

    # Join all the individual ticket strings into one large text block.
    # The '---' separator helps the RAG system distinguish between different documents.
    final_text = "\n\n---\n\n".join(formatted_tickets)

    # Write the final text to the output file.
    with open(OUTPUT_TXT_PATH, 'w', encoding='utf-8') as f:
        f.write(final_text)
        
    print(f"✅ Successfully processed {len(formatted_tickets)} tickets.")
    print(f"🚀 Your RAG-ready file has been saved as '{OUTPUT_TXT_PATH}'.")


if __name__ == "__main__":
    preprocess_tickets()